import { request, styled, useAsync, useOptions, useSelector } from '@topwrite/common';
import { memo, useMemo } from 'react';
import Loader from '../../components/loader';
import { PaneHeader } from '../../components/pane';
import Action from './action';
import { Conversation, Provider, useContext } from './context';
import HistoryList from './history-list';
import InputBox from './input-box';
import MessageList from './message-list';
import useFormatMessage from '../../lib/use-format-message';
import Welcome from './welcome';

function AssistantContent() {
    const { windowState, messages, authorized } = useContext();
    const t = useFormatMessage();

    const getWindowTitle = () => {
        switch (windowState) {
            case 'history':
                return t('assistant.window.history');
            case 'settings':
                return t('assistant.window.settings');
            default:
                return t('assistant.window.chat');
        }
    };

    const renderWindowContent = () => {
        switch (windowState) {
            case 'history':
                return <HistoryList />;
            case 'settings':
                return <div style={{ padding: '1rem' }}>{t('assistant.settings_developing')}</div>;
            default:
                return <>
                    {messages.length == 0 ? <Welcome /> : <MessageList messages={messages} />}
                    <InputBox />
                </>;
        }
    };

    return <Container>
        <Header action={<Action />}>
            {getWindowTitle()}
        </Header>
        <Body>
            {renderWindowContent()}
        </Body>
    </Container>;
}

const Assistant = memo(() => {
    const { assistant } = useOptions();

    const { result: { conversation } = {} } = useAsync<{ conversation: Conversation }>(async () => {
        const { data } = await request.get(`${assistant.base}`);
        return data;
    }, []);

    if (conversation === undefined) {
        return <Loader />;
    }

    return <Provider conversation={conversation}>
        <AssistantContent />
    </Provider>;
}, () => true);

export default Assistant;

const Container = styled.div`
    height: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
`;

const Header = styled(PaneHeader)`
    height: 43px;
    padding: 6px 4px 6px 10px;
`;

const Body = styled.div`
    flex: auto;
    overflow: hidden;
    position: relative;
    display: flex;
    flex-direction: column;
`;

import { styled } from '@topwrite/common';

export default function Welcome() {

    return <WelcomeScreen>
        <AssistantAvatar>
            <AvatarIcon>🤖</AvatarIcon>
            <VibeGlow />
        </AssistantAvatar>
        <WelcomeSubtitle>Very Intelligent Brain Enhancement</WelcomeSubtitle>
        <WelcomeDescription>
            用AI增强你的大脑潜能，进入最佳创作状态
        </WelcomeDescription>
        <CapabilityGrid>
            <CapabilityCard>
                <CapabilityIcon>🧠</CapabilityIcon>
                <CapabilityTitle>智能增强创作思维</CapabilityTitle>
                <CapabilityDesc>提升你的创造力和想象力</CapabilityDesc>
            </CapabilityCard>
            <CapabilityCard>
                <CapabilityIcon>⚡</CapabilityIcon>
                <CapabilityTitle>提升创作效率和质量</CapabilityTitle>
                <CapabilityDesc>快速生成高质量内容</CapabilityDesc>
            </CapabilityCard>
            <CapabilityCard>
                <CapabilityIcon>🎯</CapabilityIcon>
                <CapabilityTitle>进入沉浸式创作体验</CapabilityTitle>
                <CapabilityDesc>专注创作，减少干扰</CapabilityDesc>
            </CapabilityCard>
            <CapabilityCard>
                <CapabilityIcon>📝</CapabilityIcon>
                <CapabilityTitle>智能文档编辑助手</CapabilityTitle>
                <CapabilityDesc>文档结构优化和内容完善</CapabilityDesc>
            </CapabilityCard>
        </CapabilityGrid>
        <StartTip>
            开始对话，让AI成为你的创作伙伴
        </StartTip>
    </WelcomeScreen>;
}


const WelcomeScreen = styled.div`
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex: 1;
    text-align: center;
    padding: 2rem;
    color: var(--bs-body-color);
`;

const AssistantAvatar = styled.div`
    position: relative;
    margin-bottom: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
`;

const AvatarIcon = styled.div`
    font-size: 3.5rem;
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #3c60ff, #5a7cff);
    border-radius: 50%;
    position: relative;
    z-index: 2;
`;

const VibeGlow = styled.div`
    position: absolute;
    width: 100px;
    height: 100px;
    background: linear-gradient(135deg, #3c60ff, #5a7cff);
    border-radius: 50%;
    opacity: 0.3;
    animation: vibeGlow 2s ease-in-out infinite alternate;

    @keyframes vibeGlow {
        0% {
            transform: scale(1);
            opacity: 0.3;
        }
        100% {
            transform: scale(1.2);
            opacity: 0.1;
        }
    }
`;

const WelcomeSubtitle = styled.div`
    font-size: 0.9rem;
    color: var(--bs-secondary);
    margin-bottom: 1rem;
    font-weight: 500;
    letter-spacing: 0.5px;
`;

const WelcomeDescription = styled.p`
    font-size: 1.1rem;
    color: var(--ttw-color);
    margin-bottom: 2rem;
    max-width: 400px;
    line-height: 1.6;
`;

const CapabilityGrid = styled.div`
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    margin: 2rem 0;
    width: 100%;
    max-width: 500px;
`;

const CapabilityCard = styled.div`
    padding: 1rem;
    background: var(--ttw-capability-card-background);
    border-radius: 12px;
    border: 1px solid var(--bs-border-color);
    text-align: center;
    transition: all 0.3s ease;

    &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px var(--ttw-capability-card-hover-shadow);
        border-color: var(--bs-primary);
    }
`;

const CapabilityIcon = styled.div`
    font-size: 1.8rem;
    margin-bottom: 0.5rem;
`;

const CapabilityTitle = styled.div`
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--ttw-color);
    margin-bottom: 0.25rem;
`;

const CapabilityDesc = styled.div`
    font-size: 0.75rem;
    color: var(--bs-secondary);
    line-height: 1.3;
`;

const StartTip = styled.div`
    font-size: 0.9rem;
    color: var(--bs-secondary);
    font-style: italic;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 1rem;
`;

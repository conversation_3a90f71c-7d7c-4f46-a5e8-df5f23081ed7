import { useLocalStorageState } from '@topwrite/common';
import { GoPlus } from 'react-icons/go';
import { VscChromeClose, VscHistory, VscReply, VscSettingsGear } from 'react-icons/vsc';
import Button from '../../components/button';
import Tooltip from '../../components/tooltip';
import { useContext } from './context';
import useFormatMessage from '../../lib/use-format-message';

export default function Action() {
    const { reset, windowState, setWindowState, authorized } = useContext();
    const t = useFormatMessage();

    const [, setAssistantWindow] = useLocalStorageState('assistant.window', true);

    if (windowState !== 'chat') {
        return <div className={'d-flex gap-1'}>
            <Tooltip tooltip={t('assistant.back')}>
                <Button variant='light' onClick={() => setWindowState('chat')}>
                    <VscReply />
                </Button>
            </Tooltip>
        </div>;
    }

    return <div className={'d-flex'}>
        {authorized && <Tooltip tooltip={'assistant.new'}>
            <Button variant='light' onClick={() => reset()}><GoPlus /></Button>
        </Tooltip>}
        {authorized && <Tooltip tooltip={'assistant.history'}>
            <Button variant='light' onClick={() => setWindowState('history')}>
                <VscHistory />
            </Button>
        </Tooltip>}
        {authorized && <Tooltip tooltip={'assistant.settings'}>
            <Button className={'d-none'} variant='light' onClick={() => setWindowState('settings')}>
                <VscSettingsGear />
            </Button>
        </Tooltip>}
        <Tooltip tooltip={t('assistant.close')}>
            <Button variant='light' onClick={() => setAssistantWindow(false)}>
                <VscChromeClose />
            </Button>
        </Tooltip>
    </div>;
}

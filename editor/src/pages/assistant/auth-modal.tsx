import { useState } from 'react';
import { Modal } from 'react-bootstrap';
import { styled, useSelector, useActions } from '@topwrite/common';
import Button from '../../components/button';
import useFormatMessage from '../../lib/use-format-message';

interface AuthModalProps {
    show: boolean;
    onHide: () => void;
}

export default function AuthModal({ show, onHide }: AuthModalProps) {
    const [token, setToken] = useState('');
    const [loading, setLoading] = useState(false);
    const { config } = useSelector('book');
    const { updateConfig } = useActions('book');
    const t = useFormatMessage();

    const handleSave = async () => {
        if (!token.trim()) {
            return;
        }

        setLoading(true);
        try {
            // 保存加密的token到配置
            await updateConfig((config) => {
                config.setValue('assistant.token', token.trim());
            });
            onHide();
            // 刷新页面以重新检查授权状态
            window.location.reload();
        } catch (error) {
            console.error('保存token失败:', error);
        } finally {
            setLoading(false);
        }
    };

    const handleCancel = () => {
        setToken('');
        onHide();
    };

    return (
        <Modal show={show} onHide={handleCancel} backdrop="static" centered>
            <Modal.Header closeButton>
                <Modal.Title>授权 ThinkAI</Modal.Title>
            </Modal.Header>
            <Modal.Body>
                <Description>
                    请输入您的 ThinkAI 令牌以启用 AI 助手功能
                </Description>
                <TokenInput>
                    <input
                        type="password"
                        className="form-control"
                        placeholder="请输入 ThinkAI 令牌"
                        value={token}
                        onChange={(e) => setToken(e.target.value)}
                        onKeyDown={(e) => {
                            if (e.key === 'Enter' && !loading) {
                                handleSave();
                            }
                        }}
                        autoFocus
                    />
                </TokenInput>
                <HelpText>
                    令牌将被安全加密存储在本地配置中
                </HelpText>
            </Modal.Body>
            <Modal.Footer>
                <Button variant="secondary" onClick={handleCancel}>
                    取消
                </Button>
                <Button 
                    onClick={handleSave} 
                    loading={loading}
                    disabled={!token.trim()}
                >
                    保存
                </Button>
            </Modal.Footer>
        </Modal>
    );
}

const Description = styled.div`
    margin-bottom: 1rem;
    color: var(--bs-body-color);
    line-height: 1.5;
`;

const TokenInput = styled.div`
    margin-bottom: 0.75rem;
    
    .form-control {
        font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
    }
`;

const HelpText = styled.div`
    font-size: 0.875rem;
    color: var(--bs-secondary);
    line-height: 1.4;
`;
